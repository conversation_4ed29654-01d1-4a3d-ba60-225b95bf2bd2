import{w as y,v as b,a as o,o as a}from"./chunk-C37GKA54-BAI9agUh.js";import{S as m,I as S,_ as u,q as p,i as j}from"./index-C9EGKWKr.js";import"./index-BCQUowBv.js";function R({}){return[{title:"StoreSEO Agency - Register"},{name:"description",content:"Create your StoreSEO Agency account"}]}const A=y(function(){const g=b(),[s,h]=o.useState({email:"",password:""}),[i,l]=o.useState({}),[r,d]=o.useState(!1),w=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,x=()=>{const e={};return s.email?w.test(s.email)||(e.email="Please enter a valid email address"):e.email="Email is required",s.password?s.password.length<6&&(e.password="Password must be at least 6 characters long"):e.password="Password is required",l(e),Object.keys(e).length===0},c=e=>t=>{h(n=>({...n,[e]:t})),i[e]&&l(n=>({...n,[e]:void 0}))},f=async e=>{if(e.preventDefault(),!!x()){d(!0);try{await new Promise(t=>setTimeout(t,1e3)),g("/dashboard")}catch(t){console.error("Registration failed:",t)}finally{d(!1)}}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-lg w-full space-y-8",children:a.jsx(m,{children:a.jsx(S,{children:a.jsxs("form",{onSubmit:f,children:[a.jsx(u,{as:"h1",variant:"headingLg",alignment:"center",children:"Create Your Account"}),a.jsx(u,{as:"p",variant:"bodyMd",tone:"subdued",alignment:"center",children:"Join StoreSEO Agency to get started"}),a.jsxs(m,{paddingBlockStart:"400",paddingBlockEnd:"400",children:[a.jsx(p,{label:"Email",type:"email",value:s.email,onChange:c("email"),error:i.email,autoComplete:"email",placeholder:"Enter your email address",disabled:r}),a.jsx(p,{label:"Password",type:"password",value:s.password,onChange:c("password"),error:i.password,autoComplete:"new-password",placeholder:"Create a secure password",disabled:r})]}),a.jsx(j,{variant:"primary",size:"large",fullWidth:!0,submit:!0,loading:r,disabled:r,children:r?"Creating Account...":"Register"})]})})})})})});export{A as default,R as meta};
