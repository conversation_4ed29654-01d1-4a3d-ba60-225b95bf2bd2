import { <PERSON><PERSON>ta<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@storeware/polaris";
import { useNavigate } from "react-router";
import type { Route } from "./+types/dashboard";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Dashboard" },
    { name: "description", content: "Your StoreSEO Agency dashboard" },
  ];
}

export default function Dashboard() {
  const navigate = useNavigate();

  const handleLogout = () => {
    // In a real app, you would clear authentication tokens here
    navigate("/auth/register");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Text as="h1" variant="headingLg">
              StoreSEO Agency Dashboard
            </Text>
            <Button variant="tertiary" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <BlockStack gap="600">
            {/* Welcome Section */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  Welcome to StoreSEO Agency! 🎉
                </Text>
                <Text as="p" variant="bodyMd">
                  Congratulations! You have successfully created your account and are now logged into your dashboard.
                  This is a demo dashboard to showcase the registration flow.
                </Text>
              </BlockStack>
            </Card>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm" tone="subdued">
                    Total Projects
                  </Text>
                  <Text as="p" variant="heading2xl">
                    0
                  </Text>
                  <Text as="p" variant="bodySm" tone="subdued">
                    Start by creating your first project
                  </Text>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm" tone="subdued">
                    SEO Score
                  </Text>
                  <Text as="p" variant="heading2xl">
                    --
                  </Text>
                  <Text as="p" variant="bodySm" tone="subdued">
                    No data available yet
                  </Text>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm" tone="subdued">
                    Active Campaigns
                  </Text>
                  <Text as="p" variant="heading2xl">
                    0
                  </Text>
                  <Text as="p" variant="bodySm" tone="subdued">
                    Ready to launch your first campaign
                  </Text>
                </BlockStack>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  Quick Actions
                </Text>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button variant="primary" fullWidth>
                    Create Project
                  </Button>
                  <Button variant="secondary" fullWidth>
                    SEO Analysis
                  </Button>
                  <Button variant="secondary" fullWidth>
                    Keyword Research
                  </Button>
                  <Button variant="secondary" fullWidth>
                    View Reports
                  </Button>
                </div>
              </BlockStack>
            </Card>

            {/* Getting Started */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  Getting Started
                </Text>
                <BlockStack gap="300">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Text as="span" variant="bodySm">
                          1
                        </Text>
                      </div>
                    </div>
                    <div className="flex-1">
                      <Text as="p" variant="bodyMd">
                        <strong>Set up your profile:</strong> Complete your account information and preferences
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Text as="span" variant="bodySm">
                          2
                        </Text>
                      </div>
                    </div>
                    <div className="flex-1">
                      <Text as="p" variant="bodyMd">
                        <strong>Create your first project:</strong> Add your website and start optimizing
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Text as="span" variant="bodySm">
                          3
                        </Text>
                      </div>
                    </div>
                    <div className="flex-1">
                      <Text as="p" variant="bodyMd">
                        <strong>Run your first SEO analysis:</strong> Get insights and recommendations
                      </Text>
                    </div>
                  </div>
                </BlockStack>

                <InlineStack align="start">
                  <Button variant="primary">
                    Get Started
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          </BlockStack>
        </div>
      </div>
    </div>
  );
}
