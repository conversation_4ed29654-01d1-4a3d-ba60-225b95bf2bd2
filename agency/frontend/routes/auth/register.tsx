import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, TextField } from "@storeware/polaris";
import { useState } from "react";
import { useNavigate } from "react-router";
import type { Route } from "./+types/register";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Register" },
    { name: "description", content: "Create your StoreSEO Agency account" },
  ];
}

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email?: string;
  password?: string;
}

export default function Register() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters long";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData) => (value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Navigate to dashboard on successful registration
      navigate("/dashboard");
    } catch (error) {
      console.error("Registration failed:", error);
      // Handle registration error here
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8">
        <Box>
          <Card>
            <form onSubmit={handleSubmit}>
                <Text
                  as="h1"
                  variant="headingLg"
                  alignment="center"
                >
                  Create Your Account
                </Text>
                <Text
                  as="p"
                  variant="bodyMd"
                  tone="subdued"
                  alignment="center"
                >
                  Join StoreSEO Agency to get started
                </Text>
            

                {/* Form Fields */}
                <Box paddingBlockStart="400" paddingBlockEnd="400" >
                <TextField
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange("email")}
                  error={errors.email}
                  autoComplete="email"
                  placeholder="Enter your email address"
                  disabled={isSubmitting}
                />

                <TextField
                  label="Password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange("password")}
                  error={errors.password}
                  autoComplete="new-password"
                  placeholder="Create a secure password"
                  disabled={isSubmitting}
                />
                </Box>
                

                {/* Submit Button */}
                <Button
                  variant="primary"
                  size="large"
                  fullWidth
                  submit
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Creating Account..." : "Register"}
                </Button>
            </form>
          </Card>
        </Box>
      </div>
    </div>
  );
}
